
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { UserPlus, Plus, Search, BarChart3 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import { AddCandidateDialog } from "@/components/candidate/AddCandidateDialog";

export function QuickActions() {
  const navigate = useNavigate();
  const [showAddCandidate, setShowAddCandidate] = useState(false);

  const actions = [
    {
      title: "Add Candidate",
      description: "Add a new candidate to your talent pool",
      icon: UserPlus,
      onClick: () => setShowAddCandidate(true),
      variant: "default" as const,
    },
    {
      title: "Create Job",
      description: "Post a new job opening",
      icon: Plus,
      onClick: () => navigate('/jobs'),
      variant: "outline" as const,
    },
    {
      title: "Search Talent",
      description: "Find candidates for your roles",
      icon: Search,
      onClick: () => navigate('/search'),
      variant: "outline" as const,
    },
    {
      title: "View Analytics",
      description: "Check your hiring metrics",
      icon: BarChart3,
      onClick: () => navigate('/analytics'),
      variant: "outline" as const,
    },
  ];

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {actions.map((action) => (
              <Button
                key={action.title}
                variant={action.variant}
                onClick={action.onClick}
                className="h-auto min-h-[80px] p-4 flex flex-col items-start gap-2 text-left whitespace-normal"
              >
                <div className="flex items-center gap-2 w-full">
                  <action.icon className="h-4 w-4 flex-shrink-0" />
                  <span className="font-medium text-sm leading-tight">{action.title}</span>
                </div>
                <span className="text-xs opacity-80 leading-relaxed break-words w-full">{action.description}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>
      
      <AddCandidateDialog 
        open={showAddCandidate} 
        onOpenChange={setShowAddCandidate} 
      />
    </>
  );
}
