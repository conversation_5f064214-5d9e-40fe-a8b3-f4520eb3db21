import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { SavedSearch } from "@/types/search";
import { Bookmark, Trash2 } from "lucide-react";

interface SavedSearchesProps {
  searches: SavedSearch[];
  onSelect: (search: SavedSearch) => void;
  onDelete: (id: string) => void;
}

export function SavedSearches({ searches, onSelect, onDelete }: SavedSearchesProps) {
  if (searches.length === 0) return null;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          <Bookmark className="h-4 w-4 mr-2" />
          Saved Searches
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-[300px]">
        {searches.map((search) => (
          <DropdownMenuItem
            key={search.id}
            className="flex items-center justify-between"
          >
            <span
              className="flex-1 cursor-pointer"
              onClick={() => onSelect(search)}
            >
              {search.name}
            </span>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={(e) => {
                e.stopPropagation();
                onDelete(search.id);
              }}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}