// JSON Schema definitions for all workflow node types

export const nodeSchemas: Record<string, any> = {
  // Trigger Nodes
  'new-application': {
    type: 'object',
    title: 'New Application Trigger',
    description: 'Triggers when a new application is received',
    properties: {
      jobTypes: {
        type: 'string',
        title: 'Job Types',
        description: 'Filter by job types',
        enum: ['all', 'full-time', 'part-time', 'contract', 'internship'],
        default: 'all'
      },
      notifyTeam: {
        type: 'boolean',
        title: 'Notify Team',
        description: 'Send notification to team members',
        default: true
      },
      autoScreen: {
        type: 'boolean',
        title: 'Auto Screen',
        description: 'Automatically screen applications',
        default: false
      }
    },
    required: ['jobTypes']
  },

  'application-status': {
    type: 'object',
    title: 'Application Status Change',
    description: 'Triggers when application status changes',
    properties: {
      statusType: {
        type: 'string',
        title: 'Status Type',
        description: 'Which status changes to monitor',
        enum: ['any', 'submitted', 'screening', 'interview', 'offer', 'rejected', 'withdrawn'],
        default: 'any'
      },
      includeInternal: {
        type: 'boolean',
        title: 'Include Internal Changes',
        description: 'Include status changes made by the system',
        default: false
      }
    },
    required: ['statusType']
  },

  'scheduled-trigger': {
    type: 'object',
    title: 'Scheduled Trigger',
    description: 'Triggers on a schedule',
    properties: {
      scheduleType: {
        type: 'string',
        title: 'Schedule Type',
        enum: ['hourly', 'daily', 'weekly', 'monthly'],
        default: 'daily'
      },
      time: {
        type: 'string',
        title: 'Time',
        description: 'Time in HH:MM format',
        pattern: '^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$',
        default: '09:00'
      },
      timezone: {
        type: 'string',
        title: 'Timezone',
        default: 'UTC'
      }
    },
    required: ['scheduleType', 'time']
  },

  // Action Nodes
  'send-email': {
    type: 'object',
    title: 'Send Email',
    description: 'Send email to candidates or team members',
    properties: {
      template: {
        type: 'string',
        title: 'Email Template',
        enum: ['welcome', 'interview', 'offer', 'rejection', 'follow_up', 'assessment', 'document_request', 'custom'],
        default: 'welcome'
      },
      customMessage: {
        type: 'string',
        title: 'Custom Message',
        description: 'Custom email content (used when template is "custom")',
        format: 'textarea',
        maxLength: 5000
      },
      ccRecruiter: {
        type: 'boolean',
        title: 'CC Recruiter',
        description: 'Send copy to assigned recruiter',
        default: false
      },
      trackOpens: {
        type: 'boolean',
        title: 'Track Opens',
        description: 'Track email opens and clicks',
        default: true
      },
      attachments: {
        type: 'array',
        title: 'Attachments',
        description: 'List of attachment types to include',
        items: {
          type: 'string',
          enum: ['job_description', 'company_brochure', 'benefits_guide']
        }
      }
    },
    required: ['template']
  },

  'ai-screen': {
    type: 'object',
    title: 'AI Screening',
    description: 'Screen candidates using AI',
    properties: {
      criteria: {
        type: 'string',
        title: 'Screening Criteria',
        enum: ['experience', 'skills', 'education', 'comprehensive', 'document_verification', 'cultural_fit'],
        default: 'comprehensive'
      },
      minScore: {
        type: 'number',
        title: 'Minimum Score (%)',
        description: 'Minimum passing score',
        minimum: 0,
        maximum: 100,
        default: 70
      },
      customInstructions: {
        type: 'string',
        title: 'Custom Instructions',
        description: 'Additional screening criteria',
        format: 'textarea',
        maxLength: 1000
      },
      generateSummary: {
        type: 'boolean',
        title: 'Generate Summary',
        description: 'Generate AI summary of screening results',
        default: true
      },
      compareToJobDescription: {
        type: 'boolean',
        title: 'Compare to Job Description',
        description: 'Match candidate against specific job requirements',
        default: true
      }
    },
    required: ['criteria', 'minScore']
  },

  'schedule-interview': {
    type: 'object',
    title: 'Schedule Interview',
    description: 'Schedule interviews with candidates',
    properties: {
      interviewType: {
        type: 'string',
        title: 'Interview Type',
        enum: ['technical', 'behavioral', 'cultural', 'final', 'screening', 'panel'],
        default: 'technical'
      },
      duration: {
        type: 'number',
        title: 'Duration (minutes)',
        minimum: 15,
        maximum: 480,
        default: 60
      },
      interviewers: {
        type: 'string',
        title: 'Interviewers',
        description: 'Comma-separated email addresses'
      },
      location: {
        type: 'string',
        title: 'Location/Platform',
        enum: ['zoom', 'teams', 'google_meet', 'phone', 'onsite'],
        default: 'zoom'
      },
      sendCalendarInvite: {
        type: 'boolean',
        title: 'Send Calendar Invite',
        default: true
      },
      sendPrepEmail: {
        type: 'boolean',
        title: 'Send Preparation Email',
        default: true
      },
      leadTime: {
        type: 'number',
        title: 'Lead Time (days)',
        description: 'Days in advance to schedule',
        minimum: 1,
        maximum: 30,
        default: 3
      }
    },
    required: ['interviewType', 'duration']
  },

  // Condition Nodes
  'skills-match': {
    type: 'object',
    title: 'Skills Match Check',
    description: 'Check if candidate has required skills',
    properties: {
      requiredSkills: {
        type: 'string',
        title: 'Required Skills',
        description: 'Comma-separated list of required skills'
      },
      minMatchPercentage: {
        type: 'number',
        title: 'Minimum Match %',
        minimum: 0,
        maximum: 100,
        default: 70
      },
      considerSimilarSkills: {
        type: 'boolean',
        title: 'Consider Similar Skills',
        description: 'Match related/similar skills',
        default: true
      },
      skillCategories: {
        type: 'array',
        title: 'Skill Categories',
        items: {
          type: 'string',
          enum: ['technical', 'soft_skills', 'languages', 'tools', 'certifications']
        }
      }
    },
    required: ['requiredSkills', 'minMatchPercentage']
  },

  'experience-check': {
    type: 'object',
    title: 'Experience Check',
    description: 'Check candidate experience requirements',
    properties: {
      minYears: {
        type: 'number',
        title: 'Minimum Years',
        minimum: 0,
        maximum: 50,
        default: 3
      },
      industry: {
        type: 'string',
        title: 'Industry',
        description: 'Required industry experience',
        default: 'any'
      },
      considerInternships: {
        type: 'boolean',
        title: 'Include Internships',
        default: true
      },
      specificRoles: {
        type: 'string',
        title: 'Specific Roles',
        description: 'Comma-separated list of role titles'
      }
    },
    required: ['minYears']
  },

  // Output Nodes
  'update-status': {
    type: 'object',
    title: 'Update Candidate Status',
    description: 'Update the status of a candidate',
    properties: {
      newStatus: {
        type: 'string',
        title: 'New Status',
        enum: ['new', 'screening', 'shortlisted', 'interview', 'assessment', 'reference_check', 'offer', 'hired', 'rejected', 'withdrawn'],
        default: 'screening'
      },
      statusNote: {
        type: 'string',
        title: 'Status Note',
        description: 'Note about the status change',
        format: 'textarea',
        maxLength: 500
      },
      notifyCandidate: {
        type: 'boolean',
        title: 'Notify Candidate',
        default: true
      },
      notifyTeam: {
        type: 'boolean',
        title: 'Notify Team',
        default: false
      }
    },
    required: ['newStatus']
  },

  'add-to-pool': {
    type: 'object',
    title: 'Add to Talent Pool',
    description: 'Add candidate to a talent pool',
    properties: {
      poolName: {
        type: 'string',
        title: 'Pool Name',
        enum: ['engineering', 'sales', 'marketing', 'design', 'operations', 'executive', 'general'],
        default: 'general'
      },
      tags: {
        type: 'string',
        title: 'Tags',
        description: 'Comma-separated tags'
      },
      followUpDate: {
        type: 'string',
        title: 'Follow-up Date',
        format: 'date'
      },
      notes: {
        type: 'string',
        title: 'Notes',
        format: 'textarea',
        maxLength: 1000
      },
      priority: {
        type: 'string',
        title: 'Priority',
        enum: ['low', 'medium', 'high'],
        default: 'medium'
      }
    },
    required: ['poolName']
  },

  // Transformation Nodes
  'delay': {
    type: 'object',
    title: 'Delay',
    description: 'Add a delay to the workflow',
    properties: {
      delayType: {
        type: 'string',
        title: 'Delay Type',
        enum: ['minutes', 'hours', 'days'],
        default: 'hours'
      },
      delayValue: {
        type: 'number',
        title: 'Delay Value',
        minimum: 1,
        maximum: 365,
        default: 1
      },
      businessDaysOnly: {
        type: 'boolean',
        title: 'Business Days Only',
        description: 'Skip weekends and holidays',
        default: false
      }
    },
    required: ['delayType', 'delayValue']
  },

  'data-transform': {
    type: 'object',
    title: 'Data Transformation',
    description: 'Transform workflow data',
    properties: {
      transformationType: {
        type: 'string',
        title: 'Transformation Type',
        enum: ['map', 'filter', 'reduce', 'custom'],
        default: 'map'
      },
      mappingRules: {
        type: 'object',
        title: 'Mapping Rules',
        properties: {
          sourceField: {
            type: 'string',
            title: 'Source Field'
          },
          targetField: {
            type: 'string',
            title: 'Target Field'
          },
          transformation: {
            type: 'string',
            title: 'Transformation',
            enum: ['uppercase', 'lowercase', 'trim', 'parse_json', 'stringify']
          }
        }
      },
      customScript: {
        type: 'string',
        title: 'Custom Script',
        description: 'JavaScript transformation code',
        format: 'textarea'
      }
    },
    required: ['transformationType']
  }
};
