import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { Settings2, X } from "lucide-react";
import { DynamicFormGenerator } from "./DynamicFormGenerator";
import { nodeSchemas } from "./nodeSchemas";
import { ScrollArea } from "@/components/ui/scroll-area";

interface WorkflowNodeConfigProps {
  node: any;
  onClose: () => void;
  onUpdate: (nodeId: string, data: any) => void;
}

export function WorkflowNodeConfig({ node, onClose, onUpdate }: WorkflowNodeConfigProps) {
  const nodeType = node.data.originalId || node.data.id;
  const schema = nodeSchemas[nodeType];
  const [config, setConfig] = useState(node.data.config || getDefaultConfig(nodeType));

  const handleSave = () => {
    onUpdate(node.id, {
      ...node.data,
      config,
    });
    toast.success("Node configuration updated");
  };

  return (
    <Card className="fixed right-4 top-4 w-80 z-50 shadow-lg">
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Settings2 className="w-4 h-4" />
          <h3 className="font-medium">Configure {node.data.label}</h3>
        </div>
        <Button variant="ghost" size="icon" onClick={onClose}>
          <X className="w-4 h-4" />
        </Button>
      </div>
      <ScrollArea className="h-[calc(100vh-200px)]">
        <div className="p-4 space-y-4">
          {schema ? (
            <DynamicFormGenerator
              schema={schema}
              values={config}
              onChange={setConfig}
            />
          ) : (
            <div className="text-sm text-muted-foreground">
              No configuration options available for this node type.
            </div>
          )}
          <div className="flex justify-end gap-2 pt-4">
            <Button variant="outline" onClick={onClose}>Cancel</Button>
            <Button onClick={handleSave}>Save Changes</Button>
          </div>
        </div>
      </ScrollArea>
    </Card>
  );
}

function getDefaultConfig(nodeType: string) {
  switch (nodeType) {
    // Triggers
    case "new-application":
      return {
        jobTypes: "all",
        notifyTeam: true,
        autoScreen: false,
      };
    case "application-status":
      return {
        statusType: "any",
        includeInternal: false,
      };
    case "scheduled-trigger":
      return {
        scheduleType: "daily",
        time: "09:00",
      };
    case "document-upload":
      return {
        documentType: "all",
        fileFormat: "all",
        autoProcess: true,
        notifyUpload: true,
      };
    case "webhook-trigger":
      return {
        event: "all",
        secretKey: "",
        validateSignature: true,
      };
    case "database-trigger":
      return {
        table: "candidates",
        operation: "all",
        includeOldData: false,
      };

    // Actions
    case "send-email":
      return {
        template: "welcome",
        ccRecruiter: false,
        trackOpens: true,
      };
    case "schedule-interview":
      return {
        interviewType: "technical",
        duration: "60",
        sendCalendarInvite: true,
      };
    case "ai-screen":
      return {
        criteria: "comprehensive",
        minScore: "70",
        generateSummary: true,
      };
    case "send-assessment":
      return {
        assessmentType: "technical",
        timeLimit: "60",
        autoGrade: true,
      };
    case "notify-team":
      return {
        channel: "email",
        teamMembers: "all",
        highPriority: false,
      };
    case "data-enrichment":
      return {
        source: "linkedin",
        enrichContact: true,
        enrichWork: true,
        enrichEducation: false,
        enrichSkills: true,
        autoUpdate: true,
      };
    case "create-task":
      return {
        title: "",
        description: "",
        assignee: "recruiter",
        dueDays: "3",
        priority: "medium",
      };
    case "add-tag":
      return {
        tags: "",
        category: "skills",
        color: "blue",
        overwriteExisting: false,
        notifyTeam: false,
      };
    case "score-candidate":
      return {
        scoringMethod: "manual",
        manualScore: "70",
        scoreCategory: "overall",
        scoreNotes: "",
        updateOverall: true,
        notifyChanges: false,
      };

    // Conditions
    case "experience-check":
      return {
        minYears: "3",
        industry: "any",
        considerInternships: true,
      };
    case "education-check":
      return {
        minDegree: "bachelors",
        fieldOfStudy: "any",
        considerEquivalentExp: true,
      };
    case "skills-match":
      return {
        requiredSkills: "",
        minMatchPercentage: "70",
        considerSimilarSkills: true,
      };
    case "location-check":
      return {
        locationType: "onsite",
        requiredLocation: "",
        maxDistance: "50",
      };

    // Outputs
    case "update-status":
      return {
        newStatus: "screening",
        statusNote: "",
        notifyCandidate: true,
      };
    case "add-to-pool":
      return {
        poolName: "engineering",
        tags: "",
        followUpDate: "",
      };
    case "send-message":
      return {
        template: "follow-up",
        messageContent: "",
        sendImmediately: true,
      };

    default:
      return {};
  }
}