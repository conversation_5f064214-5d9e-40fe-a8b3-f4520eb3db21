import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Pencil, 
  Trash2, 
  Play, 
  Plus, 
  Search, 
  CalendarClock, 
  CalendarIcon, 
  Eye,
  Copy,
  Download,
  Power,
  PowerOff,
  MoreVertical
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { 
  useWorkflowConfigurations, 
  useDeleteWorkflowConfiguration,
  useUpdateWorkflowConfiguration,
  useCreateWorkflowConfiguration
} from "@/hooks/useWorkflowConfigurations";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { RunWorkflowDialog } from "./RunWorkflowDialog";
import { useNavigate } from "react-router-dom";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { WorkflowHistory } from "./WorkflowHistory";
import { WorkflowScheduler } from "./WorkflowScheduler";
import { useWorkflowExecution } from "@/hooks/useWorkflowExecution";

export function WorkflowManager() {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { data: workflows = [], isLoading } = useWorkflowConfigurations();
  const deleteWorkflow = useDeleteWorkflowConfiguration();
  const updateWorkflow = useUpdateWorkflowConfiguration();
  const createWorkflow = useCreateWorkflowConfiguration();
  const [searchQuery, setSearchQuery] = useState("");
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState<any>(null);
  const [isRunDialogOpen, setIsRunDialogOpen] = useState(false);
  const [activeTab, setActiveTab] = useState("workflows");
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false);
  const [selectedWorkflows, setSelectedWorkflows] = useState<Set<string>>(new Set());
  const [showBulkActions, setShowBulkActions] = useState(false);
  const { executeWorkflow } = useWorkflowExecution();

  const filteredWorkflows = workflows.filter(workflow => 
    workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (workflow.description && workflow.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const handleRunWorkflow = (workflow: any) => {
    setSelectedWorkflow(workflow);
    setIsRunDialogOpen(true);
  };
  
  const handleQuickRun = async (workflow: any) => {
    try {
      toast({
        title: "Running Workflow",
        description: `Starting execution of "${workflow.name}"...`
      });
      
      await executeWorkflow(workflow.id, {});
      
      toast({
        title: "Workflow Started",
        description: "Workflow execution has been initiated"
      });
    } catch (error) {
      console.error('Error running workflow:', error);
      toast({
        title: "Error",
        description: "Failed to run workflow. Please try again.",
        variant: "destructive"
      });
    }
  };

  const handleEditWorkflow = (workflow: any) => {
    // Navigate to the workflow editor with the selected workflow ID
    navigate('/ai-workflows?edit=' + workflow.id);
  };

  const handleScheduleWorkflow = (workflow: any) => {
    setSelectedWorkflow(workflow);
    setIsScheduleDialogOpen(true);
  };

  const confirmDelete = (workflow: any) => {
    setSelectedWorkflow(workflow);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteWorkflow = async () => {
    if (!selectedWorkflow) return;
    
    try {
      await deleteWorkflow.mutateAsync(selectedWorkflow.id);
      setIsDeleteDialogOpen(false);
      setSelectedWorkflow(null);
    } catch (error) {
      console.error('Error deleting workflow:', error);
    }
  };

  const toggleWorkflowSelection = (workflowId: string) => {
    const newSelection = new Set(selectedWorkflows);
    if (newSelection.has(workflowId)) {
      newSelection.delete(workflowId);
    } else {
      newSelection.add(workflowId);
    }
    setSelectedWorkflows(newSelection);
    setShowBulkActions(newSelection.size > 0);
  };

  const selectAllWorkflows = () => {
    if (selectedWorkflows.size === filteredWorkflows.length) {
      setSelectedWorkflows(new Set());
      setShowBulkActions(false);
    } else {
      setSelectedWorkflows(new Set(filteredWorkflows.map(w => w.id)));
      setShowBulkActions(true);
    }
  };

  const handleBulkDuplicate = async () => {
    const selectedIds = Array.from(selectedWorkflows);
    
    for (const id of selectedIds) {
      const workflow = workflows.find(w => w.id === id);
      if (workflow) {
        try {
          await createWorkflow.mutateAsync({
            name: `${workflow.name} (Copy)`,
            description: workflow.description,
            config: workflow.config,
            is_active: workflow.is_active
          });
        } catch (error) {
          console.error(`Error duplicating workflow ${workflow.name}:`, error);
          toast({
            title: "Error",
            description: `Failed to duplicate ${workflow.name}`,
            variant: "destructive"
          });
        }
      }
    }
    
    toast({
      title: "Success",
      description: `Duplicated ${selectedIds.length} workflow${selectedIds.length > 1 ? 's' : ''}`
    });
    setSelectedWorkflows(new Set());
    setShowBulkActions(false);
  };

  const handleBulkExport = () => {
    const selectedIds = Array.from(selectedWorkflows);
    const exportData = workflows
      .filter(w => selectedIds.includes(w.id))
      .map(({ id, created_at, updated_at, user_id, ...workflow }) => workflow);
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `workflows-export-${new Date().toISOString()}.json`;
    a.click();
    window.URL.revokeObjectURL(url);
    
    toast({
      title: "Success",
      description: `Exported ${selectedIds.length} workflow${selectedIds.length > 1 ? 's' : ''}`
    });
    setSelectedWorkflows(new Set());
    setShowBulkActions(false);
  };

  const handleBulkToggleActive = async (activate: boolean) => {
    const selectedIds = Array.from(selectedWorkflows);
    
    for (const id of selectedIds) {
      const workflow = workflows.find(w => w.id === id);
      if (workflow) {
        try {
          await updateWorkflow.mutateAsync({
            id,
            is_active: activate
          });
        } catch (error) {
          console.error(`Error updating workflow ${workflow.name}:`, error);
          toast({
            title: "Error",
            description: `Failed to update ${workflow.name}`,
            variant: "destructive"
          });
        }
      }
    }
    
    toast({
      title: "Success",
      description: `${activate ? 'Activated' : 'Deactivated'} ${selectedIds.length} workflow${selectedIds.length > 1 ? 's' : ''}`
    });
    setSelectedWorkflows(new Set());
    setShowBulkActions(false);
  };

  const handleBulkDelete = async () => {
    const selectedIds = Array.from(selectedWorkflows);
    
    if (!confirm(`Are you sure you want to delete ${selectedIds.length} workflow${selectedIds.length > 1 ? 's' : ''}? This action cannot be undone.`)) {
      return;
    }
    
    for (const id of selectedIds) {
      try {
        await deleteWorkflow.mutateAsync(id);
      } catch (error) {
        console.error(`Error deleting workflow:`, error);
        toast({
          title: "Error",
          description: `Failed to delete some workflows`,
          variant: "destructive"
        });
      }
    }
    
    toast({
      title: "Success",
      description: `Deleted ${selectedIds.length} workflow${selectedIds.length > 1 ? 's' : ''}`
    });
    setSelectedWorkflows(new Set());
    setShowBulkActions(false);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>AI Workflows</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">Loading workflows...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>AI Workflows</CardTitle>
        <Button onClick={() => navigate('/ai-workflows?tab=canvas')}>
          <Plus className="h-4 w-4 mr-2" />
          Create Workflow
        </Button>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="workflows">Workflows</TabsTrigger>
            <TabsTrigger value="history">Execution History</TabsTrigger>
          </TabsList>
          
          <TabsContent value="workflows">
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search workflows..."
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              {showBulkActions && (
                <div className="flex items-center justify-between p-3 bg-accent/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">
                      {selectedWorkflows.size} workflow{selectedWorkflows.size > 1 ? 's' : ''} selected
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleBulkDuplicate}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Duplicate
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleBulkExport}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="sm">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Bulk Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleBulkToggleActive(true)}>
                          <Power className="h-4 w-4 mr-2" />
                          Activate
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleBulkToggleActive(false)}>
                          <PowerOff className="h-4 w-4 mr-2" />
                          Deactivate
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          className="text-destructive"
                          onClick={handleBulkDelete}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              )}
            </div>
            
            <ScrollArea className="h-[500px]">
              {filteredWorkflows.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  {searchQuery ? "No workflows match your search" : "No workflows found. Create your first workflow to get started!"}
                </div>
              ) : (
                <>
                  {filteredWorkflows.length > 1 && (
                    <div className="flex items-center gap-2 mb-3 pb-3 border-b">
                      <Checkbox
                        checked={selectedWorkflows.size === filteredWorkflows.length}
                        onCheckedChange={selectAllWorkflows}
                      />
                      <Label className="text-sm font-medium cursor-pointer" onClick={selectAllWorkflows}>
                        Select all ({filteredWorkflows.length} workflows)
                      </Label>
                    </div>
                  )}
                  <div className="space-y-4">
                    {filteredWorkflows.map((workflow) => (
                      <Card key={workflow.id} className="hover:bg-accent/5 transition-colors">
                        <CardContent className="p-4">
                          <div className="flex items-start gap-3">
                            <Checkbox
                              checked={selectedWorkflows.has(workflow.id)}
                              onCheckedChange={() => toggleWorkflowSelection(workflow.id)}
                              className="mt-1"
                            />
                            <div className="flex-1">
                              <div className="flex justify-between items-start mb-2">
                                <div>
                                  <h3 className="font-medium">{workflow.name}</h3>
                                  {workflow.description && (
                                    <p className="text-sm text-muted-foreground">{workflow.description}</p>
                                  )}
                                </div>
                                <Badge variant="outline">
                                  {workflow.is_active ? 'Active' : 'Inactive'}
                                </Badge>
                              </div>
                              <div className="text-xs text-muted-foreground mb-4">
                                Created: {new Date(workflow.created_at).toLocaleDateString()}
                                {workflow.created_at !== workflow.updated_at && (
                                  <span> • Updated: {new Date(workflow.updated_at).toLocaleDateString()}</span>
                                )}
                              </div>
                              <div className="flex gap-2 justify-end">
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => handleQuickRun(workflow)}
                                  title="Quick Run"
                                >
                                  <Play className="h-4 w-4 mr-1" />
                                  Run
                                </Button>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => handleRunWorkflow(workflow)}
                                  title="Run with Options"
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  Details
                                </Button>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => handleScheduleWorkflow(workflow)}
                                >
                                  <CalendarClock className="h-4 w-4 mr-1" />
                                  Schedule
                                </Button>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => handleEditWorkflow(workflow)}
                                >
                                  <Pencil className="h-4 w-4 mr-1" />
                                  Edit
                                </Button>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => confirmDelete(workflow)}
                                  className="text-destructive"
                                >
                                  <Trash2 className="h-4 w-4 mr-1" />
                                  Delete
                                </Button>
                              </div>
                            </div>
                          </div>
                      </CardContent>
                    </Card>
                  ))}
                  </div>
                </>
              )}
            </ScrollArea>
          </TabsContent>
          
          <TabsContent value="history">
            <WorkflowHistory />
          </TabsContent>
        </Tabs>
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Workflow</DialogTitle>
          </DialogHeader>
          <p>
            Are you sure you want to delete the workflow "{selectedWorkflow?.name}"? 
            This action cannot be undone.
          </p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteWorkflow}
              disabled={deleteWorkflow.isPending}
            >
              {deleteWorkflow.isPending ? "Deleting..." : "Delete Workflow"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Run Workflow Dialog */}
      {selectedWorkflow && (
        <RunWorkflowDialog
          isOpen={isRunDialogOpen}
          onClose={() => setIsRunDialogOpen(false)}
          workflowId={selectedWorkflow.id}
          workflowName={selectedWorkflow.name}
        />
      )}
      
      {/* Schedule Workflow Dialog */}
      {selectedWorkflow && (
        <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
          <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Schedule Workflow</DialogTitle>
            </DialogHeader>
            <WorkflowScheduler 
              workflowId={selectedWorkflow.id} 
              workflowName={selectedWorkflow.name} 
            />
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
}