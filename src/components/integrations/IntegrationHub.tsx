import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Settings,
  Globe,
  MessageSquare,
  Mail,
  Calendar,
  Webhook,
  CheckCircle,
  AlertCircle,
  Plus,
  Link as LinkIcon
} from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useToast } from "@/hooks/use-toast";
import { useIntegrations, useUpdateIntegration } from "@/hooks/useIntegrations";
import { useInitializeIntegrations, useConnectIntegration, useDisconnectIntegration } from "@/hooks/useIntegrationManagement";

interface Integration {
  id: string;
  name: string;
  description: string;
  icon: any;
  category: 'communication' | 'calendar' | 'database' | 'analytics' | 'other';
  status: 'connected' | 'available' | 'coming_soon';
  settings?: { [key: string]: any };
}

export function IntegrationHub() {
  const [selectedIntegration, setSelectedIntegration] = useState<Integration | null>(null);
  const [isConfigDialogOpen, setIsConfigDialogOpen] = useState(false);
  const { toast } = useToast();
  
  // Use database integration data
  const { data: integrationData = [], isLoading, error } = useIntegrations();
  const updateIntegration = useUpdateIntegration();
  const initializeIntegrations = useInitializeIntegrations();
  const connectIntegration = useConnectIntegration();
  const disconnectIntegration = useDisconnectIntegration();

  // Initialize default integrations on component mount
  useEffect(() => {
    if (integrationData.length === 0 && !isLoading) {
      initializeIntegrations.mutate();
    }
  }, [integrationData.length, isLoading]);

  // Icon mapping for integrations
  const getIcon = (name: string) => {
    switch (name.toLowerCase()) {
      case 'slack': return MessageSquare;
      case 'email service': return Mail;
      case 'google calendar': return Calendar;
      case 'custom webhooks': return Webhook;
      case 'rest api': return Globe;
      case 'zapier': return LinkIcon;
      default: return Settings;
    }
  };

  // Transform database data to component format
  const integrations: Integration[] = integrationData.map(item => ({
    id: item.id,
    name: item.name,
    description: item.description || '',
    icon: getIcon(item.name),
    category: item.category,
    status: item.status,
    settings: item.settings
  }));

  const handleConnect = async (integration: Integration) => {
    if (integration.status === 'coming_soon') {
      toast({
        title: "Coming Soon",
        description: `${integration.name} integration is coming soon!`
      });
      return;
    }

    if (integration.status === 'connected') {
      // If already connected, open configuration dialog
      setSelectedIntegration(integration);
      setIsConfigDialogOpen(true);
      return;
    }

    // For available integrations, connect with basic settings
    try {
      const basicSettings = {
        apiKey: '',
        webhookUrl: '',
        enabled: true,
        connectedAt: new Date().toISOString()
      };

      await connectIntegration.mutateAsync({
        integrationName: integration.name,
        settings: basicSettings
      });
    } catch (error) {
      console.error('Failed to connect integration:', error);
    }
  };

  const handleDisconnect = async (integration: Integration) => {
    try {
      await disconnectIntegration.mutateAsync(integration.name);
    } catch (error) {
      console.error('Failed to disconnect integration:', error);
    }
  };

  const handleSaveConfig = async () => {
    if (!selectedIntegration) return;
    
    try {
      await updateIntegration.mutateAsync({
        id: selectedIntegration.id,
        status: 'connected'
      });
      
      toast({
        title: "Configuration Saved",
        description: "Integration settings have been saved successfully."
      });
      setIsConfigDialogOpen(false);
      setSelectedIntegration(null);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save integration configuration.",
        variant: "destructive"
      });
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'connected':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />Connected</Badge>;
      case 'available':
        return <Badge variant="outline">Available</Badge>;
      case 'coming_soon':
        return <Badge variant="secondary">Coming Soon</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return CheckCircle;
      case 'available': return Plus;
      case 'coming_soon': return AlertCircle;
      default: return Settings;
    }
  };

  const categories = [
    { id: 'all', label: 'All Integrations' },
    { id: 'communication', label: 'Communication' },
    { id: 'calendar', label: 'Calendar' },
    { id: 'database', label: 'Database' },
    { id: 'analytics', label: 'Analytics' },
    { id: 'other', label: 'Other' }
  ];

  const [selectedCategory, setSelectedCategory] = useState('all');

  const filteredIntegrations = selectedCategory === 'all' 
    ? integrations 
    : integrations.filter(i => i.category === selectedCategory);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-6 w-24" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-10 w-10 rounded-lg" />
                    <Skeleton className="h-6 w-24" />
                  </div>
                  <Skeleton className="h-6 w-20" />
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-10 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Integration Hub</h2>
            <p className="text-muted-foreground">
              Connect TalentCRM with your favorite tools and services
            </p>
          </div>
        </div>
        <div className="text-center p-8">
          <p className="text-muted-foreground">Failed to load integrations. Please try again.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Integration Hub</h2>
          <p className="text-muted-foreground">
            Connect TalentCRM with your favorite tools and services
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="text-sm">
            {integrations.filter(i => i.status === 'connected').length} Connected
          </Badge>
        </div>
      </div>

      <Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
        <TabsList className="grid w-full grid-cols-3 sm:grid-cols-6 h-auto overflow-x-auto">
          {categories.map(category => (
            <TabsTrigger key={category.id} value={category.id} className="text-xs sm:text-sm px-2 sm:px-3 py-2 whitespace-nowrap">
              <span className="hidden sm:inline">{category.label}</span>
              <span className="sm:hidden">
                {category.id === 'all' ? 'All' : 
                 category.id === 'communication' ? 'Comm' :
                 category.id === 'calendar' ? 'Cal' :
                 category.id === 'database' ? 'DB' :
                 category.id === 'analytics' ? 'Analytics' :
                 'Other'}
              </span>
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={selectedCategory} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredIntegrations.map((integration) => {
              const IconComponent = integration.icon;
              const StatusIcon = getStatusIcon(integration.status);

              return (
                <Card key={integration.id} className="hover:shadow-md transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="p-2 rounded-lg bg-primary/10">
                          <IconComponent className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{integration.name}</CardTitle>
                        </div>
                      </div>
                      {getStatusBadge(integration.status)}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-sm text-muted-foreground">
                      {integration.description}
                    </p>

                    <div className="flex flex-col sm:flex-row gap-2">
                      {integration.status === 'connected' ? (
                        <>
                          <Button
                            variant="outline" 
                            size="sm" 
                            onClick={() => handleConnect(integration)}
                            className="flex-1 w-full"
                          >
                            <Settings className="w-4 h-4 mr-2" />
                            Configure
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => handleDisconnect(integration)}
                            className="w-full sm:w-auto"
                          >
                            Disconnect
                          </Button>
                        </>
                      ) : (
                        <Button 
                          onClick={() => handleConnect(integration)}
                          disabled={integration.status === 'coming_soon'}
                          className="w-full"
                          size="sm"
                        >
                          <StatusIcon className="w-4 h-4 mr-2" />
                          {integration.status === 'coming_soon' ? 'Coming Soon' : 'Connect'}
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>

      {/* Configuration Dialog */}
      <Dialog open={isConfigDialogOpen} onOpenChange={setIsConfigDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedIntegration && (
                <selectedIntegration.icon className="h-5 w-5" />
              )}
              Configure {selectedIntegration?.name}
            </DialogTitle>
            <DialogDescription>
              Set up your {selectedIntegration?.name} integration settings.
            </DialogDescription>
          </DialogHeader>

          {selectedIntegration && (
            <div className="space-y-4">
              {selectedIntegration.name === 'Slack' && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="webhook-url">Slack Webhook URL</Label>
                    <Input
                      id="webhook-url"
                      placeholder="https://hooks.slack.com/services/..."
                      defaultValue={selectedIntegration.settings?.webhook_url}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="channel">Default Channel</Label>
                    <Input
                      id="channel"
                      placeholder="#general"
                      defaultValue={selectedIntegration.settings?.channel}
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch 
                      id="notifications" 
                      defaultChecked={selectedIntegration.settings?.notifications_enabled}
                    />
                    <Label htmlFor="notifications">Enable notifications</Label>
                  </div>
                </>
              )}

              {selectedIntegration.name === 'Email Service' && (
                <>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="smtp-server">SMTP Server</Label>
                      <Input
                        id="smtp-server"
                        defaultValue={selectedIntegration.settings?.smtp_server}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="port">Port</Label>
                      <Input
                        id="port"
                        type="number"
                        defaultValue={selectedIntegration.settings?.port}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="username">Username</Label>
                    <Input
                      id="username"
                      type="email"
                      defaultValue={selectedIntegration.settings?.username}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter your email password"
                    />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch 
                      id="secure" 
                      defaultChecked={selectedIntegration.settings?.secure}
                    />
                    <Label htmlFor="secure">Use secure connection (TLS)</Label>
                  </div>
                </>
              )}

              {selectedIntegration.name === 'Custom Webhooks' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label>Webhook Endpoints</Label>
                    <div className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-center gap-2">
                        <Input placeholder="https://your-api.com/webhook" />
                        <Button size="sm" variant="outline">
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Events: candidate_created, interview_scheduled, job_posted
                      </div>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex gap-2 pt-4">
                <Button onClick={handleSaveConfig}>
                  Save Configuration
                </Button>
                <Button variant="outline" onClick={() => setIsConfigDialogOpen(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}