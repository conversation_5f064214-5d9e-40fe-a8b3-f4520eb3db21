import { MetricsCards } from "./MetricsCards";
import { ApplicationsChart } from "./ApplicationsChart";
import { HiringTrends } from "./HiringTrends";
import { SkillGapAnalysis } from "./SkillGapAnalysis";
import { DiversityMetrics } from "./DiversityMetrics";
import { SourceEffectiveness } from "./SourceEffectiveness";
import { SalaryRecommendations } from "./SalaryRecommendations";
import { useState } from "react";
import { Maximize2, RefreshCw, Loader2 } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAnalyticsMetrics } from "@/hooks/useAnalyticsMetrics";
import { useAnalyticsApplications } from "@/hooks/useAnalyticsApplications";
import { useAnalyticsSkills } from "@/hooks/useAnalyticsSkills";
import { useAnalyticsSources } from "@/hooks/useAnalyticsSources";
import { useAnalyticsDiversity } from "@/hooks/useAnalyticsDiversity";
import { useAnalyticsSalary } from "@/hooks/useAnalyticsSalary";
import { useGenerateAnalyticsData } from "@/hooks/useAnalyticsDataGeneration";

export const AnalyticsDashboard = () => {
  const [expandedCard, setExpandedCard] = useState<string | null>(null);
  const { toast } = useToast();
  const generateAnalytics = useGenerateAnalyticsData();

  // Load all analytics data
  const { data: metricsData = [] } = useAnalyticsMetrics();
  const { data: applicationsData = [] } = useAnalyticsApplications();
  const { data: skillsData = [] } = useAnalyticsSkills();
  const { data: sourcesData = [] } = useAnalyticsSources();
  const { data: diversityData = [] } = useAnalyticsDiversity();
  const { data: salaryData = [] } = useAnalyticsSalary();

  // Check if we have data in all tables
  const hasAllData = metricsData.length > 0 && 
                    applicationsData.length > 0 && 
                    skillsData.length > 0 && 
                    sourcesData.length > 0 && 
                    diversityData.length > 0 && 
                    salaryData.length > 0;

  const handleExpand = (cardId: string) => {
    setExpandedCard(cardId);
  };

  const handleClose = () => {
    setExpandedCard(null);
  };

  const handleRefreshAnalytics = async () => {
    try {
      await generateAnalytics.mutateAsync();
      // The hooks will automatically refetch the data
    } catch (error) {
      console.error('Failed to refresh analytics:', error);
    }
  };

  return (
    <div className="space-y-6">
      {!hasAllData && (
        <div className="bg-muted p-4 rounded-lg mb-6">
          <h3 className="font-medium mb-2">Analytics Data</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Some analytics data is being generated from mock data. In a production environment, 
            this would be populated with real data from your recruitment activities.
          </p>
          <Button
            variant="outline"
            onClick={handleRefreshAnalytics}
            disabled={generateAnalytics.isPending}
          >
            {generateAnalytics.isPending ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="mr-2 h-4 w-4" />
            )}
            {generateAnalytics.isPending ? 'Refreshing...' : 'Refresh Analytics Data'}
          </Button>
        </div>
      )}

      <MetricsCards />
      <ApplicationsChart />
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="relative">
          <button
            onClick={() => handleExpand('hiring')}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <HiringTrends expanded={false} />
        </div>
        <div className="relative">
          <button
            onClick={() => handleExpand('skills')}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <SkillGapAnalysis expanded={false} />
        </div>
        <div className="relative">
          <button
            onClick={() => handleExpand('diversity')}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <DiversityMetrics expanded={false} />
        </div>
        <div className="relative">
          <button
            onClick={() => handleExpand('source')}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <SourceEffectiveness expanded={false} />
        </div>
        <div className="relative">
          <button
            onClick={() => handleExpand('salary')}
            className="absolute top-4 right-4 z-10 p-1 hover:bg-gray-100 rounded-full"
          >
            <Maximize2 className="h-4 w-4" />
          </button>
          <SalaryRecommendations expanded={false} />
        </div>
      </div>

      <Dialog open={expandedCard === 'hiring'} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none flex flex-col">
          <DialogHeader>
            <DialogTitle>Hiring Trends</DialogTitle>
          </DialogHeader>
          <div className="flex-1">
            <HiringTrends expanded={true} />
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={expandedCard === 'skills'} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none flex flex-col">
          <DialogHeader>
            <DialogTitle>Skill Gap Analysis</DialogTitle>
          </DialogHeader>
          <div className="flex-1">
            <SkillGapAnalysis expanded={true} />
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={expandedCard === 'diversity'} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none flex flex-col">
          <DialogHeader>
            <DialogTitle>Diversity Metrics</DialogTitle>
          </DialogHeader>
          <div className="flex-1">
            <DiversityMetrics expanded={true} />
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={expandedCard === 'source'} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none flex flex-col">
          <DialogHeader>
            <DialogTitle>Source Effectiveness</DialogTitle>
          </DialogHeader>
          <div className="flex-1">
            <SourceEffectiveness expanded={true} />
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={expandedCard === 'salary'} onOpenChange={handleClose}>
        <DialogContent className="w-[90vw] h-[90vh] max-w-none flex flex-col">
          <DialogHeader>
            <DialogTitle>Salary Recommendations</DialogTitle>
          </DialogHeader>
          <div className="flex-1">
            <SalaryRecommendations expanded={true} />
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};