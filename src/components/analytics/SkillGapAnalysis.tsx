

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { RadarChart, Radar, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Tooltip, ResponsiveContainer } from 'recharts';
import { Brain, TrendingUp, AlertCircle, Star } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { Progress } from "@/components/ui/progress";
import { useAnalyticsSkills } from "@/hooks/useAnalyticsSkills";
import { Skeleton } from "@/components/ui/skeleton";


export const SkillGapAnalysis = ({ expanded = false }: { expanded?: boolean }) => {
  const { data: skillsData = [], isLoading } = useAnalyticsSkills();
  
  // The data is already transformed in the hook, so we can use it directly
  const skillGapAnalysis = skillsData;
  
  if (isLoading) {
    return (
      <div className={expanded ? 'h-full overflow-y-auto' : ''}>
        <Card className={expanded ? 'h-full border-0 shadow-none' : ''}>
          <CardHeader className="flex flex-row items-center justify-between">
            <Skeleton className="h-6 w-48" />
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent className={`max-h-[75vh] ${expanded ? 'overflow-y-auto' : 'h-[400px]'}`}>
            <Skeleton className="h-full w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }
  const getGapSeverity = (gap: number) => {
    if (gap <= 5) return { color: 'bg-green-500', text: 'Minor Gap' };
    if (gap <= 10) return { color: 'bg-yellow-500', text: 'Moderate Gap' };
    return { color: 'bg-red-500', text: 'Significant Gap' };
  };

  const calculateSuccessProbability = () => {
    const totalImpact = skillGapAnalysis.reduce((sum, skill) => sum + skill.successImpact, 0);
    return Math.round((totalImpact / (skillGapAnalysis.length * 100)) * 100);
  };

  const successProbability = calculateSuccessProbability();

  return (
    <div className={expanded ? 'h-full overflow-y-auto' : ''}>
      <Card className={expanded ? 'h-full border-0 shadow-none' : ''}>
        <CardHeader className="flex flex-row items-center justify-between">
          <div className="flex items-center gap-4">
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-4 w-4" />
              Skill Gap Analysis
            </CardTitle>
            <HoverCard>
              <HoverCardTrigger asChild>
                <AlertCircle className="h-4 w-4 text-muted-foreground cursor-help" />
              </HoverCardTrigger>
              <HoverCardContent className="w-80">
                <div className="space-y-2">
                  <h4 className="font-medium">AI-Powered Analysis</h4>
                  <p className="text-sm text-muted-foreground">
                    This analysis compares skills against requirements and predicts success probability
                    based on skill matches and gaps.
                  </p>
                </div>
              </HoverCardContent>
            </HoverCard>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Star className="h-4 w-4 text-yellow-500" />
              <span className="text-sm font-medium">
                Success Probability: {successProbability}%
              </span>
            </div>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </div>
        </CardHeader>
        <CardContent className={`max-h-[75vh] ${expanded ? 'overflow-y-auto' : 'h-[400px]'}`}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 h-full">
            <ResponsiveContainer width="100%" height="100%">
              <RadarChart data={skillGapAnalysis}>
                <PolarGrid />
                <PolarAngleAxis dataKey="skill" />
                <PolarRadiusAxis />
                <Radar 
                  name="Current Level" 
                  dataKey="current" 
                  stroke="#3b82f6" 
                  fill="#3b82f6" 
                  fillOpacity={0.6} 
                />
                <Radar 
                  name="Required Level" 
                  dataKey="required" 
                  stroke="#10b981" 
                  fill="#10b981" 
                  fillOpacity={0.6} 
                />
                <Tooltip />
              </RadarChart>
            </ResponsiveContainer>
            <div className="space-y-4 overflow-auto max-h-full pr-2">
              {skillGapAnalysis.map((skill) => {
                const severity = getGapSeverity(skill.gap);
                return (
                  <Card key={skill.skill} className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{skill.skill}</h4>
                      <Badge className={severity.color + " text-white"}>
                        {severity.text}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm text-muted-foreground">
                        <span>Current: {skill.current}%</span>
                        <span>Required: {skill.required}%</span>
                      </div>
                      <Progress value={(skill.current / skill.required) * 100} />
                      <div className="flex justify-between text-sm">
                        <span className="text-muted-foreground">Success Impact</span>
                        <span className="font-medium">{skill.successImpact}%</span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-2">
                        {skill.recommendation}
                      </p>
                    </div>
                  </Card>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

