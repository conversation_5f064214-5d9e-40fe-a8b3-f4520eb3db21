import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/layout/AppSidebar";
import { AppHeader } from "@/components/layout/AppHeader";
import { FeedbackWidget } from "@/components/feedback/FeedbackWidget";

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  return (
    <SidebarProvider defaultOpen={false}>
      <div className="min-h-screen flex w-full">
        <AppSidebar />
        <div className="flex-1 flex flex-col transition-all duration-300 ease-linear peer-data-[state=collapsed]:ml-[--sidebar-width-icon] peer-data-[state=expanded]:ml-[--sidebar-width]">
          <AppHeader />
          <div className="flex-1 container mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8 max-w-[1600px] animate-fade-in">
            {children}
          </div>
        </div>
      </div>
      <FeedbackWidget />
    </SidebarProvider>
  );
}
