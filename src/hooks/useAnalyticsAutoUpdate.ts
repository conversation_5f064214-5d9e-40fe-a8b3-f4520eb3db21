import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { AnalyticsDataService } from '@/services/AnalyticsDataService';
import { useRealtimeSubscription } from './useRealtimeSubscription';

/**
 * Hook that automatically updates analytics data when relevant data changes
 * This maintains the real-time nature of analytics displays throughout the app
 */
export const useAnalyticsAutoUpdate = () => {
  const { user } = useAuth();

  // Subscribe to candidates table changes
  const candidatesSubscription = useRealtimeSubscription(
    'candidates',
    'public',
    {
      event: '*',
      filter: `user_id=eq.${user?.id}`
    }
  );

  // Subscribe to jobs table changes
  const jobsSubscription = useRealtimeSubscription(
    'jobs',
    'public',
    {
      event: '*',
      filter: `user_id=eq.${user?.id}`
    }
  );

  // Subscribe to events table changes
  const eventsSubscription = useRealtimeSubscription(
    'events',
    'public',
    {
      event: '*',
      filter: `user_id=eq.${user?.id}`
    }
  );

  // Update analytics when data changes
  useEffect(() => {
    if (!user) return;

    const updateAnalytics = async () => {
      try {
        await AnalyticsDataService.updateAnalyticsTables(user.id);
        console.log('Analytics data updated automatically');
      } catch (error) {
        console.error('Failed to auto-update analytics:', error);
      }
    };

    // Debounce updates to avoid too frequent calls
    const timeoutId = setTimeout(updateAnalytics, 2000);

    return () => clearTimeout(timeoutId);
  }, [
    user,
    candidatesSubscription.lastEvent,
    jobsSubscription.lastEvent,
    eventsSubscription.lastEvent
  ]);

  return {
    isUpdating: false // Could add loading state if needed
  };
};
