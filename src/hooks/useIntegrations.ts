import { useMutation } from "@tanstack/react-query";
import { IntegrationsService, IntegrationData, CreateIntegrationData, UpdateIntegrationData } from "@/services/IntegrationsService";
import { useAuth } from "@/contexts/AuthContext";
import { useToast } from "@/hooks/use-toast";
import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";

export const useIntegrations = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  
  // Real-time integrations subscription
  const { records: integrations = [], isLoading } = useRealtimeCollection(
    'integrations',
    async () => {
      if (!user?.id) return [];
      
      try {
        // Try to get existing data, or initialize with defaults
        const existingData = await IntegrationsService.getIntegrations(user.id);
        if (existingData.length > 0) {
          return existingData;
        }
        
        // Initialize with default integrations if none exist
        const defaultIntegrations: CreateIntegrationData[] = [
          {
            user_id: user.id,
            name: '<PERSON>lack',
            description: 'Send notifications and updates to your Slack channels',
            category: 'communication',
            status: 'available',
            settings: {
              webhook_url: '',
              channel: '#general',
              notifications_enabled: true
            }
          },
          {
            user_id: user.id,
            name: 'Email Service',
            description: 'Send automated emails to candidates and team members',
            category: 'communication',
            status: 'connected',
            settings: {
              smtp_server: 'smtp.gmail.com',
              port: 587,
              username: '<EMAIL>',
              secure: true
            }
          },
          {
            user_id: user.id,
            name: 'Google Calendar',
            description: 'Sync interviews and events with Google Calendar',
            category: 'calendar',
            status: 'available',
            settings: {}
          },
          {
            user_id: user.id,
            name: 'Custom Webhooks',
            description: 'Send data to external systems via HTTP webhooks',
            category: 'other',
            status: 'connected',
            settings: {
              endpoints: [
                { url: 'https://api.example.com/webhook', events: ['candidate_created', 'interview_scheduled'] }
              ]
            }
          },
          {
            user_id: user.id,
            name: 'REST API',
            description: 'Access TalentCRM data via our REST API',
            category: 'other',
            status: 'connected',
            settings: {}
          },
          {
            user_id: user.id,
            name: 'Zapier',
            description: 'Connect with 3000+ apps through Zapier automation',
            category: 'other',
            status: 'coming_soon',
            settings: {}
          }
        ];

        // Create default integrations
        const createdIntegrations = await Promise.all(
          defaultIntegrations.map(integration => 
            IntegrationsService.createIntegration(integration)
          )
        );

        return createdIntegrations;
      } catch (error) {
        console.error('Error in useIntegrations:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: integrations, isLoading, error: null };
};

export const useCreateIntegration = () => {
  const { toast } = useToast();
  const { user } = useAuth();

  return useMutation({
    mutationFn: (data: Omit<CreateIntegrationData, 'user_id'>) => {
      if (!user?.id) throw new Error("User not authenticated");
      return IntegrationsService.createIntegration({ ...data, user_id: user.id });
    },
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Integration Created",
        description: "Integration has been created successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      });
    }
  });
};

export const useUpdateIntegration = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (data: UpdateIntegrationData) => IntegrationsService.updateIntegration(data),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Integration Updated",
        description: "Integration has been updated successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      });
    }
  });
};

export const useDeleteIntegration = () => {
  const { toast } = useToast();

  return useMutation({
    mutationFn: (id: string) => IntegrationsService.deleteIntegration(id),
    onSuccess: () => {
      // Real-time subscription will automatically update the UI
      toast({
        title: "Integration Deleted",
        description: "Integration has been deleted successfully."
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive"
      });
    }
  });
};

export const useConnectedIntegrations = () => {
  const { user } = useAuth();
  
  // Real-time connected integrations subscription
  const { records: connectedIntegrations = [], isLoading } = useRealtimeCollection(
    'integrations',
    async () => {
      if (!user?.id) return [];
      
      try {
        return await IntegrationsService.getConnectedIntegrations(user.id);
      } catch (error) {
        console.error('Error in useConnectedIntegrations:', error);
        return [];
      }
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return { data: connectedIntegrations, isLoading, error: null };
}; 