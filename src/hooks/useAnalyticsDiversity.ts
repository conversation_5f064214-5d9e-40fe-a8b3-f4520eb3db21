import { useRealtimeCollection } from "@/hooks/useRealtimeSubscription";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface AnalyticsDiversity {
  id: string;
  user_id: string;
  category: string;
  value: number;
  total: number;
  created_at: string;
  updated_at: string;
}

const transformDiversityForChart = (diversity: AnalyticsDiversity[]) => {
  if (!diversity || !Array.isArray(diversity)) return [];
  
  return diversity.map(item => ({
    category: item.category,
    value: item.value,
    total: item.total,
    percentage: Math.round((item.value / item.total) * 100)
  }));
};

export const useAnalyticsDiversity = () => {
  const { user } = useAuth();

  // Real-time diversity analytics subscription
  const { records: diversityData = [], isLoading } = useRealtimeCollection(
    'analytics_diversity',
    async () => {
      if (!user) {
        console.log('No authenticated user, returning empty array');
        return [];
      }

      const { data, error } = await supabase
        .from('analytics_diversity')
        .select('*')
        .eq('user_id', user.id);

      if (error) {
        console.error('Error fetching analytics diversity:', error);
        throw error;
      }
      
      return transformDiversityForChart(data || []);
    },
    'public',
    `user_id=eq.${user?.id}`
  );

  return {
    data: diversityData,
    isLoading,
    error: null
  };
};