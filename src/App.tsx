import { Toaster } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AppLayout } from "@/components/layout/AppLayout";
import { AuthProvider } from "@/contexts/AuthContext";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import Dashboard from "@/pages/Dashboard";
import Jobs from "@/pages/Jobs";
import Search from "@/pages/Search";
import Messages from "@/pages/Messages";
import Calendar from "@/pages/Calendar";
import Analytics from "@/pages/Analytics";
import Settings from "@/pages/Settings";
import AIWorkflows from "@/pages/AIWorkflows";
import Auth from "@/pages/Auth";
import Index from "@/pages/Index";
import Candidates from "@/pages/Candidates";
import CandidateDetails from "@/pages/CandidateDetails";
import CandidateDocuments from "@/pages/CandidateDocuments";
import Communication from "@/pages/Communication";
import Integrations from "@/pages/Integrations";
import Tasks from "@/pages/Tasks";
import Reports from "@/pages/Reports";
import { useEffect } from "react";
import { initializeDatabase } from "@/integrations/supabase/client";
import { WorkflowRunnerPage } from "@/components/ai/workflow/WorkflowRunnerPage";

// Minimal QueryClient for mutations only (no query configuration needed)
const queryClient = new QueryClient({
  defaultOptions: {
    mutations: {
      retry: 1, // Retry failed mutations once
    },
  },
});

const App = () => {
  // Initialize database when app loads
  useEffect(() => {
    initializeDatabase();
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <BrowserRouter>
          <AuthProvider>
            <Toaster />
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/auth" element={<Auth />} />

              {/* All protected routes wrapped in AppLayout */}
              <Route path="/*" element={
                <ProtectedRoute>
                  <AppLayout>
                    <Routes>
                      <Route path="/dashboard" element={<Dashboard />} />
                      <Route path="/candidates" element={<Candidates />} />
                      <Route path="/candidates/:id" element={<CandidateDetails />} />
                      <Route path="/candidates/:id/documents" element={<CandidateDocuments />} />
                      <Route path="/jobs" element={<Jobs />} />
                      <Route path="/search" element={<Search />} />
                      <Route path="/messages" element={<Messages />} />
                      <Route path="/calendar" element={<Calendar />} />
                      <Route path="/analytics" element={<Analytics />} />
                      <Route path="/ai-workflows" element={<AIWorkflows />} />
                      <Route path="/ai-workflows/run/:id" element={<WorkflowRunnerPage />} />
                      <Route path="/communication" element={<Communication />} />
                      <Route path="/integrations" element={<Integrations />} />
                      <Route path="/tasks" element={<Tasks />} />
                      <Route path="/reports" element={<Reports />} />
                      <Route path="/settings" element={<Settings />} />
                    </Routes>
                  </AppLayout>
                </ProtectedRoute>
              } />
            </Routes>
          </AuthProvider>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;