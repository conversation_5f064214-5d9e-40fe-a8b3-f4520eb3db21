import { supabase } from "@/integrations/supabase/client";

export interface IntegrationData {
  id: string;
  user_id: string;
  name: string;
  description: string | null;
  category: 'communication' | 'calendar' | 'database' | 'analytics' | 'other';
  status: 'connected' | 'available' | 'coming_soon';
  settings: Record<string, unknown>;
  created_at: string;
  updated_at: string;
}

export interface CreateIntegrationData {
  name: string;
  description?: string;
  category: 'communication' | 'calendar' | 'database' | 'analytics' | 'other';
  status: 'connected' | 'available' | 'coming_soon';
  settings?: Record<string, unknown>;
  user_id: string;
}

export interface UpdateIntegrationData {
  id: string;
  name?: string;
  description?: string;
  status?: 'connected' | 'available' | 'coming_soon';
  settings?: Record<string, unknown>;
}

export class IntegrationsService {
  static async getIntegrations(userId: string): Promise<IntegrationData[]> {
    const { data, error } = await supabase
      .from('integrations')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async createIntegration(integrationData: CreateIntegrationData): Promise<IntegrationData> {
    const { data, error } = await supabase
      .from('integrations')
      .insert([integrationData])
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async updateIntegration(updateData: UpdateIntegrationData): Promise<IntegrationData> {
    const { id, ...updates } = updateData;
    
    const { data, error } = await supabase
      .from('integrations')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }

  static async deleteIntegration(id: string): Promise<void> {
    const { error } = await supabase
      .from('integrations')
      .delete()
      .eq('id', id);

    if (error) throw error;
  }

  static async getIntegrationsByCategory(userId: string, category: string): Promise<IntegrationData[]> {
    const { data, error } = await supabase
      .from('integrations')
      .select('*')
      .eq('user_id', userId)
      .eq('category', category)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }

  static async getConnectedIntegrations(userId: string): Promise<IntegrationData[]> {
    const { data, error } = await supabase
      .from('integrations')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'connected')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }
} 