-- Create missing analytics tables for the analytics data service

-- Create analytics_metrics table
CREATE TABLE IF NOT EXISTS public.analytics_metrics (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  metric_name TEXT NOT NULL,
  value NUMERIC NOT NULL,
  change_percentage NUMERIC DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CONSTRAINT analytics_metrics_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
  CONSTRAINT analytics_metrics_user_metric_unique UNIQUE (user_id, metric_name)
);

-- Create analytics_applications table
CREATE TABLE IF NOT EXISTS public.analytics_applications (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  date DATE NOT NULL,
  applications INTEGER NOT NULL DEFAULT 0,
  interviews INTEGER NOT NULL DEFAULT 0,
  offers INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CONSTRAINT analytics_applications_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
  CONSTRAINT analytics_applications_user_date_unique UNIQUE (user_id, date)
);

-- Update analytics_skills table to match service expectations
ALTER TABLE public.analytics_skills 
DROP COLUMN IF EXISTS current_level,
DROP COLUMN IF EXISTS required_level,
DROP COLUMN IF EXISTS gap,
DROP COLUMN IF EXISTS recommendation;

ALTER TABLE public.analytics_skills 
ADD COLUMN IF NOT EXISTS skill_name TEXT,
ADD COLUMN IF NOT EXISTS candidate_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS demand_level TEXT DEFAULT 'medium';

-- Update skill column to skill_name if needed
UPDATE public.analytics_skills SET skill_name = skill WHERE skill_name IS NULL;

-- Create analytics_sources table
CREATE TABLE IF NOT EXISTS public.analytics_sources (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  source_name TEXT NOT NULL,
  candidate_count INTEGER NOT NULL DEFAULT 0,
  conversion_rate NUMERIC DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CONSTRAINT analytics_sources_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
  CONSTRAINT analytics_sources_user_source_unique UNIQUE (user_id, source_name)
);

-- Create analytics_diversity table
CREATE TABLE IF NOT EXISTS public.analytics_diversity (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID NOT NULL,
  category TEXT NOT NULL,
  subcategory TEXT NOT NULL,
  count INTEGER NOT NULL DEFAULT 0,
  percentage NUMERIC DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  CONSTRAINT analytics_diversity_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON DELETE CASCADE,
  CONSTRAINT analytics_diversity_user_category_unique UNIQUE (user_id, category, subcategory)
);

-- Update analytics_salary table to match service expectations
ALTER TABLE public.analytics_salary 
ADD COLUMN IF NOT EXISTS min_salary NUMERIC,
ADD COLUMN IF NOT EXISTS max_salary NUMERIC,
ADD COLUMN IF NOT EXISTS avg_salary NUMERIC;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_analytics_metrics_user_id ON public.analytics_metrics USING btree (user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_applications_user_id ON public.analytics_applications USING btree (user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_applications_date ON public.analytics_applications USING btree (date);
CREATE INDEX IF NOT EXISTS idx_analytics_sources_user_id ON public.analytics_sources USING btree (user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_diversity_user_id ON public.analytics_diversity USING btree (user_id);

-- Enable RLS on new tables
ALTER TABLE public.analytics_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics_sources ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.analytics_diversity ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can manage their own analytics_metrics"
  ON public.analytics_metrics
  FOR ALL
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can manage their own analytics_applications"
  ON public.analytics_applications
  FOR ALL
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can manage their own analytics_sources"
  ON public.analytics_sources
  FOR ALL
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can manage their own analytics_diversity"
  ON public.analytics_diversity
  FOR ALL
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Enable realtime for analytics tables
ALTER PUBLICATION supabase_realtime ADD TABLE public.analytics_metrics;
ALTER PUBLICATION supabase_realtime ADD TABLE public.analytics_applications;
ALTER PUBLICATION supabase_realtime ADD TABLE public.analytics_sources;
ALTER PUBLICATION supabase_realtime ADD TABLE public.analytics_diversity;
